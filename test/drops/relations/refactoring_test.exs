defmodule Drops.Relations.RefactoringTest do
  use Drops.OperationCase, async: true

  describe "refactored query API" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "module-level functions exist and are callable" do
      # Test that the module-level functions exist and can be called directly
      # This verifies that the refactoring successfully moved functions to module level

      # These functions should exist on the Drops.Relation module with Ecto.Repo-compatible signatures
      assert function_exported?(Drops.Relation, :get, 2)
      assert function_exported?(Drops.Relation, :get!, 2)
      assert function_exported?(Drops.Relation, :get_by, 2)
      assert function_exported?(Drops.Relation, :get_by!, 2)
      assert function_exported?(Drops.Relation, :all, 2)
      assert function_exported?(Drops.Relation, :one, 2)
      assert function_exported?(Drops.Relation, :one!, 2)
      assert function_exported?(Drops.Relation, :insert, 2)
      assert function_exported?(Drops.Relation, :insert!, 2)
      assert function_exported?(Drops.Relation, :update, 2)
      assert function_exported?(Drops.Relation, :update!, 2)
      assert function_exported?(Drops.Relation, :delete, 2)
      assert function_exported?(Drops.Relation, :delete!, 2)
      assert function_exported?(Drops.Relation, :count, 3)
      assert function_exported?(Drops.Relation, :first, 2)
      assert function_exported?(Drops.Relation, :last, 2)
      assert function_exported?(Drops.Relation, :get_by_field, 3)
    end

    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "delegating functions work correctly" do
      defmodule Test.Relations.RefactoringUsers do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      # Test that all expected functions are exported
      functions_to_test = [
        {:get, 2},
        {:get!, 2},
        {:get_by, 2},
        {:get_by!, 2},
        {:all, 1},
        {:one, 1},
        {:one!, 1},
        {:insert, 1},
        {:insert!, 1},
        {:update, 2},
        {:update!, 2},
        {:delete, 1},
        {:delete!, 1},
        {:count, 1},
        {:first, 1},
        {:last, 1}
      ]

      for {func, arity} <- functions_to_test do
        assert function_exported?(Test.Relations.RefactoringUsers, func, arity),
               "Function #{func}/#{arity} should be exported"
      end

      # Test that the functions delegate correctly by checking they can be called
      # (even if they fail due to missing data, they should reach the repo layer)

      # Clean up any existing data
      Drops.TestRepo.delete_all(Test.Relations.RefactoringUsers.Struct)

      # Test count function
      assert Test.Relations.RefactoringUsers.count() == 0

      # Test all function
      assert Test.Relations.RefactoringUsers.all() == []

      # Test that repo option can be overridden (this should work even with a different repo)
      # We'll test this by ensuring the function accepts the option without error
      assert Test.Relations.RefactoringUsers.count(nil, repo: Drops.TestRepo) == 0
    end

    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "repo option is automatically passed from use macro" do
      defmodule Test.Relations.RepoOptionUsers do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      # The functions should work without explicitly passing repo
      # because it's automatically set from the use macro
      assert Test.Relations.RepoOptionUsers.count() == 0
      assert Test.Relations.RepoOptionUsers.all() == []
    end
  end
end
