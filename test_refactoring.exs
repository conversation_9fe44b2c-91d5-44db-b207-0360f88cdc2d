# Test script to verify the refactoring works correctly

# Load the application and dependencies
Mix.install([{:drops, path: "."}])

# Test that we can access documentation for the module-level functions
IO.puts("=== Testing Documentation Access ===")

# Get documentation for the get function
{:docs_v1, _, :elixir, _, _, _, docs} = Code.fetch_docs(Drops.Relation)

get_doc = Enum.find(docs, fn
  {{:function, :get, 3}, _, _, doc, _} when is_binary(doc) -> true
  _ -> false
end)

case get_doc do
  {{:function, :get, 3}, _, _, doc, _} ->
    IO.puts("✓ Found documentation for Drops.Relation.get/3:")
    IO.puts("  #{String.slice(doc, 0, 100)}...")
  nil ->
    IO.puts("✗ No documentation found for Drops.Relation.get/3")
end

# Test that a relation module can be created and functions work
IO.puts("\n=== Testing Relation Module Creation ===")

defmodule TestRelation do
  use Drops.Relation, repo: Drops.TestRepo, name: "users"
end

# Test that the delegating functions exist
functions_to_test = [
  {:get, 2},
  {:get!, 2},
  {:get_by, 2},
  {:get_by!, 2},
  {:all, 1},
  {:one, 1},
  {:one!, 1},
  {:insert, 1},
  {:insert!, 1},
  {:update, 2},
  {:update!, 2},
  {:delete, 1},
  {:delete!, 1},
  {:count, 1},
  {:first, 1},
  {:last, 1}
]

IO.puts("Testing that all expected functions are exported:")
for {func, arity} <- functions_to_test do
  if function_exported?(TestRelation, func, arity) do
    IO.puts("  ✓ #{func}/#{arity}")
  else
    IO.puts("  ✗ #{func}/#{arity} - NOT FOUND")
  end
end

# Test that the repo option is properly passed
IO.puts("\n=== Testing Repo Option Passing ===")

# This should work without errors (even if the table doesn't exist,
# the function should be callable and delegate properly)
try do
  TestRelation.count()
  IO.puts("✓ count() function delegates correctly")
rescue
  e ->
    # Expected to fail due to table not existing, but should reach the repo call
    if String.contains?(Exception.message(e), "does not exist") or
       String.contains?(Exception.message(e), "no such table") do
      IO.puts("✓ count() function delegates correctly (table doesn't exist, as expected)")
    else
      IO.puts("✗ Unexpected error: #{Exception.message(e)}")
    end
end

IO.puts("\n=== Refactoring Test Complete ===")
